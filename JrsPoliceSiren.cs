using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class JrsPoliceSiren : MonoBehaviour
{
    private bool isSirenOn = false;
    public AudioSource sirenSound;
    public GameObject[] redLights;
    public GameObject[] blueLights;

    private JrsInputController mobileInputController;
    private JrsNewInputController newInputController;
    private JrsVehicleController vehicleController;

    private void Start()
    {
        // Get references to required components (lite version - no vehicle selector)
        vehicleController = GetComponentInParent<JrsVehicleController>();

        if (vehicleController == null)
        {
            vehicleController = GetComponent<JrsVehicleController>();
        }
    }

    private void Update()
    {
        // Lite version - always process siren input (single vehicle)
        if (vehicleController == null)
        {
            return;
        }

        // In lite version, always allow siren control since there's only one vehicle
        if (!vehicleController.canControl)
        {
            return;
        }

        if (mobileInputController == null)
        {
            mobileInputController = FindObjectOfType<JrsInputController>();
        }

        if (newInputController == null)
        {
            newInputController = FindObjectOfType<JrsNewInputController>();
        }

        if (GetSirenInput())
        {
            isSirenOn = !isSirenOn;
            ToggleSirenLights();
            ToggleLightsVisibility(redLights, isSirenOn);
            ToggleLightsVisibility(blueLights, isSirenOn);
        }
    }

    private void ToggleSirenLights()
    {
        if (isSirenOn)
        {
            // Start the light flickering effect
            InvokeRepeating("FlickerLights", 0f, 0.1f);
            // Play the siren sound
            sirenSound.Play();
        }
        else
        {
            // Stop the light flickering effect
            CancelInvoke("FlickerLights");
            // Stop the siren sound
            sirenSound.Stop();
        }
    }

    private void FlickerLights()
    {
        foreach (GameObject redLight in redLights)
        {
            if (Random.value < 0.8f)
            {
                redLight.SetActive(!redLight.activeSelf);
            }
        }

        foreach (GameObject blueLight in blueLights)
        {
            if (Random.value < 1.5f)
            {
                blueLight.SetActive(!blueLight.activeSelf);
            }
        }
    }

    private void ToggleLightsVisibility(GameObject[] lights, bool isEnabled)
    {
        foreach (GameObject light in lights)
        {
            light.SetActive(isEnabled);
        }
    }

    // Input helper method that works with both old and new input systems
    private bool GetSirenInput()
    {
        // Try New Input System first
        if (newInputController != null)
        {
            return newInputController.GetSirenInput();
        }

        // Fallback to old input system and mobile controls
        return Input.GetKeyDown(KeyCode.P) ||
               (mobileInputController != null && mobileInputController.sirenButton.IsButtonClicked());
    }
}
