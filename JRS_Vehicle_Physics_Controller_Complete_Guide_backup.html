<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JRS Vehicle Physics Controller - Complete Guide</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --warning-color: #f97316;
            --info-color: #06b6d4;
            --success-color: #22c55e;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-secondary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: var(--radius-lg);
            margin-top: 1rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        /* Navigation */
        .nav {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .nav-container {
            display: flex;
            align-items: center;
            padding: 1rem 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            margin-left: auto;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-secondary);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            transition: all 0.2s ease;
        }

        .nav-links a:hover {
            color: var(--primary-color);
            background: var(--bg-tertiary);
        }

        /* Main Content */
        .main {
            padding: 2rem 0;
        }

        .section {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--bg-tertiary);
        }

        .section-icon {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .section h2 {
            font-size: 2rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .section h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 2rem 0 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .section h4 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 1.5rem 0 1rem 0;
        }

        /* Table of Contents */
        .toc {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .toc h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .toc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .toc-item {
            background: var(--bg-primary);
            padding: 1rem;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .toc-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .toc-item a {
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .toc-item .icon {
            width: 2rem;
            height: 2rem;
            background: var(--primary-color);
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        /* Callouts */
        .callout {
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            margin: 1.5rem 0;
            border-left: 4px solid;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .callout-info {
            background: rgba(6, 182, 212, 0.1);
            border-color: var(--info-color);
            color: var(--text-primary);
        }

        .callout-warning {
            background: rgba(249, 115, 22, 0.1);
            border-color: var(--warning-color);
            color: var(--text-primary);
        }

        .callout-success {
            background: rgba(34, 197, 94, 0.1);
            border-color: var(--success-color);
            color: var(--text-primary);
        }

        .callout-danger {
            background: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
            color: var(--text-primary);
        }

        .callout-icon {
            width: 1.5rem;
            height: 1.5rem;
            flex-shrink: 0;
            margin-top: 0.125rem;
        }

        /* Code blocks */
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 0.875rem;
            line-height: 1.7;
            overflow-x: auto;
            margin: 1.5rem 0;
            border: 1px solid #334155;
        }

        /* Highlight text */
        .highlight {
            background: linear-gradient(120deg, rgba(37, 99, 235, 0.2) 0%, rgba(37, 99, 235, 0.1) 100%);
            padding: 0.125rem 0.375rem;
            border-radius: var(--radius-sm);
            font-weight: 500;
            color: var(--primary-dark);
        }

        /* Cards */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            transition: all 0.2s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .card-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .card h3 {
            margin: 0;
            font-size: 1.25rem;
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
            margin: 1.5rem 0;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-primary);
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
        }

        tr:hover {
            background: var(--bg-secondary);
        }

        /* Lists */
        ul, ol {
            margin: 1rem 0;
            padding-left: 1.5rem;
        }

        li {
            margin: 0.5rem 0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .section {
                padding: 1.5rem;
            }
            
            .toc-grid {
                grid-template-columns: 1fr;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
        }

        /* SVG Icons */
        .icon svg {
            width: 100%;
            height: 100%;
            fill: currentColor;
        }

        /* Footer */
        .footer {
            background: var(--text-primary);
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-top: 4rem;
        }

        .footer p {
            opacity: 0.8;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1>JRS Vehicle Physics Controller</h1>
                <p class="subtitle">Complete Documentation Guide - Lite Version</p>
                <div class="version-badge">Version 2.0 - Updated & Modernized</div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <div class="nav-container">
                <div class="nav-brand">
                    <strong>JRS Vehicle Controller</strong>
                </div>
                <ul class="nav-links">
                    <li><a href="#introduction">Introduction</a></li>
                    <li><a href="#getting-started">Getting Started</a></li>
                    <li><a href="#core-scripts">Core Scripts</a></li>
                    <li><a href="#features">Features</a></li>
                    <li><a href="#setup">Setup Guide</a></li>
                    <li><a href="#troubleshooting">Troubleshooting</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Table of Contents -->
            <section class="toc">
                <h2>
                    <svg class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 6h18M3 12h18M3 18h18"/>
                    </svg>
                    Table of Contents
                </h2>
                <div class="toc-grid">
                    <div class="toc-item">
                        <a href="#introduction">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            1. Introduction & Overview
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#getting-started">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                </svg>
                            </div>
                            2. Getting Started
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#core-scripts">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                                </svg>
                            </div>
                            3. Core Scripts
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#input-system">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                                </svg>
                            </div>
                            4. Input System
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#camera-system">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                                </svg>
                            </div>
                            5. Camera System
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#vehicle-features">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/>
                                </svg>
                            </div>
                            6. Vehicle Features
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#setup-guide">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                                </svg>
                            </div>
                            7. Setup Guide
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#troubleshooting">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                                </svg>
                            </div>
                            8. Troubleshooting
                        </a>
                    </div>
                </div>
            </section>

            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h2>Introduction & Overview</h2>
                </div>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="m9 12 2 2 4-4"/>
                    </svg>
                    <div>
                        <strong>Welcome to JRS Vehicle Controller Lite!</strong> This is the streamlined version of our vehicle physics system, optimized for <span class="highlight">single vehicle control</span> with all the core features you need for realistic vehicle simulation in Unity.
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                    </svg>
                    What's New in Version 2.0
                </h3>

                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <h3>Lite Version</h3>
                        </div>
                        <p>Simplified architecture focused on <strong>single vehicle control</strong>. No more complex vehicle selector systems - just pure, direct vehicle control.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                                </svg>
                            </div>
                            <h3>Modern Input System</h3>
                        </div>
                        <p>Full support for Unity's <strong>New Input System</strong> with backward compatibility. Supports keyboard, gamepad, and mobile touch controls seamlessly.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </div>
                            <h3>Enhanced Camera System</h3>
                        </div>
                        <p>Multiple camera types with smooth transitions: <strong>Follow Camera</strong>, <strong>Orbit Camera</strong>, and <strong>Dash Camera</strong> with touch and gamepad support.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99z"/>
                                </svg>
                            </div>
                            <h3>Rich Vehicle Features</h3>
                        </div>
                        <p>Complete lighting system, police sirens, door mechanics, realistic physics, and audio system - all working seamlessly together.</p>
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Prerequisites
                </h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Requirement</th>
                                <th>Version/Details</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Unity Version</strong></td>
                                <td>2021.3 LTS or newer</td>
                                <td><span style="color: var(--success-color);">✅ Required</span></td>
                            </tr>
                            <tr>
                                <td><strong>Input System Package</strong></td>
                                <td>Unity's New Input System</td>
                                <td><span style="color: var(--warning-color);">⚠️ Recommended</span></td>
                            </tr>
                            <tr>
                                <td><strong>3D Physics</strong></td>
                                <td>Enabled in Project Settings</td>
                                <td><span style="color: var(--success-color);">✅ Required</span></td>
                            </tr>
                            <tr>
                                <td><strong>Experience Level</strong></td>
                                <td>Basic Unity knowledge</td>
                                <td><span style="color: var(--info-color);">ℹ️ Helpful</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="callout callout-success">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    <div>
                        <strong>Quick Start:</strong> The system is designed to work out-of-the-box. Simply import the scripts, set up your vehicle, and you're ready to drive!
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    System Architecture
                </h3>

                <p>The JRS Vehicle Controller System is built with a <span class="highlight">modular architecture</span> that allows you to use only the components you need:</p>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Component Type</th>
                                <th>Purpose</th>
                                <th>Required</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Core Controller</strong></td>
                                <td>Main vehicle physics and movement</td>
                                <td><span style="color: var(--success-color);">✅ Yes</span></td>
                            </tr>
                            <tr>
                                <td><strong>Input System</strong></td>
                                <td>Handle player input (keyboard, mobile, gamepad)</td>
                                <td><span style="color: var(--success-color);">✅ Yes</span></td>
                            </tr>
                            <tr>
                                <td><strong>Camera Manager</strong></td>
                                <td>Multiple camera views and switching</td>
                                <td><span style="color: var(--success-color);">✅ Yes</span></td>
                            </tr>
                            <tr>
                                <td><strong>Vehicle Features</strong></td>
                                <td>Lights, doors, sirens, animations</td>
                                <td><span style="color: var(--text-muted);">❌ Optional</span></td>
                            </tr>
                            <tr>
                                <td><strong>UI Components</strong></td>
                                <td>Mobile controls and custom buttons</td>
                                <td><span style="color: var(--text-muted);">❌ Optional</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
