<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JRS Vehicle Physics Controller - Complete Guide</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --warning-color: #f97316;
            --info-color: #06b6d4;
            --success-color: #22c55e;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-secondary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: var(--radius-lg);
            margin-top: 1rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        /* Navigation */
        .nav {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .nav-container {
            display: flex;
            align-items: center;
            padding: 1rem 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            margin-left: auto;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-secondary);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            transition: all 0.2s ease;
        }

        .nav-links a:hover {
            color: var(--primary-color);
            background: var(--bg-tertiary);
        }

        /* Main Content */
        .main {
            padding: 2rem 0;
        }

        .section {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--bg-tertiary);
        }

        .section-icon {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .section h2 {
            font-size: 2rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .section h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 2rem 0 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .section h4 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 1.5rem 0 1rem 0;
        }

        /* Table of Contents */
        .toc {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .toc h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .toc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .toc-item {
            background: var(--bg-primary);
            padding: 1rem;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .toc-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .toc-item a {
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .toc-item .icon {
            width: 2rem;
            height: 2rem;
            background: var(--primary-color);
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        /* Callouts */
        .callout {
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            margin: 1.5rem 0;
            border-left: 4px solid;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .callout-info {
            background: rgba(6, 182, 212, 0.1);
            border-color: var(--info-color);
            color: var(--text-primary);
        }

        .callout-warning {
            background: rgba(249, 115, 22, 0.1);
            border-color: var(--warning-color);
            color: var(--text-primary);
        }

        .callout-success {
            background: rgba(34, 197, 94, 0.1);
            border-color: var(--success-color);
            color: var(--text-primary);
        }

        .callout-danger {
            background: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
            color: var(--text-primary);
        }

        .callout-icon {
            width: 1.5rem;
            height: 1.5rem;
            flex-shrink: 0;
            margin-top: 0.125rem;
        }

        /* Code blocks */
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 0.875rem;
            line-height: 1.7;
            overflow-x: auto;
            margin: 1.5rem 0;
            border: 1px solid #334155;
        }

        /* Highlight text */
        .highlight {
            background: linear-gradient(120deg, rgba(37, 99, 235, 0.2) 0%, rgba(37, 99, 235, 0.1) 100%);
            padding: 0.125rem 0.375rem;
            border-radius: var(--radius-sm);
            font-weight: 500;
            color: var(--primary-dark);
        }

        /* Cards */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            transition: all 0.2s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .card-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .card h3 {
            margin: 0;
            font-size: 1.25rem;
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
            margin: 1.5rem 0;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-primary);
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
        }

        tr:hover {
            background: var(--bg-secondary);
        }

        /* Lists */
        ul, ol {
            margin: 1rem 0;
            padding-left: 1.5rem;
        }

        li {
            margin: 0.5rem 0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .section {
                padding: 1.5rem;
            }
            
            .toc-grid {
                grid-template-columns: 1fr;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
        }

        /* SVG Icons */
        .icon svg {
            width: 100%;
            height: 100%;
            fill: currentColor;
        }

        /* Input Controls Styling */
        .input-controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .input-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
        }

        .input-section h4 {
            margin: 0 0 1rem 0;
            color: var(--text-primary);
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .input-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 0.75rem 0;
            padding: 0.5rem;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .input-icon {
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .key-icon {
            background: linear-gradient(145deg, #f0f0f0, #d0d0d0);
            border: 2px solid #999;
            border-radius: 6px;
            color: #333;
            font-weight: bold;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .gamepad-icon {
            background: linear-gradient(145deg, #4a90e2, #357abd);
            border-radius: 50%;
            color: white;
        }

        .touch-icon {
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            border-radius: 8px;
            color: white;
        }

        .input-description {
            flex: 1;
            color: var(--text-primary);
            font-size: 0.95rem;
        }

        .input-description strong {
            color: var(--accent-color);
        }

        /* Footer */
        .footer {
            background: var(--text-primary);
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-top: 4rem;
        }

        .footer p {
            opacity: 0.8;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1>JRS Vehicle Physics Controller</h1>
                <p class="subtitle">Complete Documentation Guide - Updated Version</p>
                <div class="version-badge">Version 2.0 - Updated & Modernized</div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <div class="nav-container">
                <div class="nav-brand">
                    <strong>JRS Vehicle Controller</strong>
                </div>
                <ul class="nav-links">
                    <li><a href="#introduction">Introduction</a></li>
                    <li><a href="#getting-started">Getting Started</a></li>
                    <li><a href="#core-scripts">Core Scripts</a></li>
                    <li><a href="#vehicle-features">Features</a></li>
                    <li><a href="#setup-guide">Setup Guide</a></li>
                    <li><a href="#troubleshooting">Troubleshooting</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Table of Contents -->
            <section class="toc">
                <h2>
                    <svg class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 6h18M3 12h18M3 18h18"/>
                    </svg>
                    Table of Contents
                </h2>
                <div class="toc-grid">
                    <div class="toc-item">
                        <a href="#introduction">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            1. Introduction & Overview
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#getting-started">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                </svg>
                            </div>
                            2. Getting Started
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#core-scripts">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                                </svg>
                            </div>
                            3. Core Scripts
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#input-system">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                                </svg>
                            </div>
                            4. Input System
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#camera-system">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                                </svg>
                            </div>
                            5. Camera System
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#vehicle-features">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/>
                                </svg>
                            </div>
                            6. Vehicle Features
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#setup-guide">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                                </svg>
                            </div>
                            7. Setup Guide
                        </a>
                    </div>
                    <div class="toc-item">
                        <a href="#troubleshooting">
                            <div class="icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                                </svg>
                            </div>
                            8. Troubleshooting
                        </a>
                    </div>
                </div>
            </section>

            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h2>Introduction & Overview</h2>
                </div>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="m9 12 2 2 4-4"/>
                    </svg>
                    <div>
                        <strong>Welcome to JRS Vehicle Controller!</strong> This is the updated version of our vehicle physics system, optimized for <span class="highlight">single vehicle control</span> with all the core features you need for realistic vehicle simulation in Unity.
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                    </svg>
                    What's New in Version 2.0
                </h3>

                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <h3>Streamlined Design</h3>
                        </div>
                        <p>Simplified architecture focused on <strong>single vehicle control</strong>. No more complex vehicle selector systems - just pure, direct vehicle control.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                                </svg>
                            </div>
                            <h3>Modern Input System</h3>
                        </div>
                        <p>Full support for Unity's <strong>New Input System</strong> with backward compatibility. Supports keyboard, gamepad, and mobile touch controls seamlessly.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </div>
                            <h3>Enhanced Camera System</h3>
                        </div>
                        <p>Multiple camera types with smooth transitions: <strong>Follow Camera</strong>, <strong>Orbit Camera</strong>, and <strong>Dash Camera</strong> with touch and gamepad support.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99z"/>
                                </svg>
                            </div>
                            <h3>Rich Vehicle Features</h3>
                        </div>
                        <p>Complete lighting system, police sirens, door mechanics, realistic physics, and audio system - all working seamlessly together.</p>
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Prerequisites
                </h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Requirement</th>
                                <th>Version/Details</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Unity Version</strong></td>
                                <td>2021.3 LTS or newer</td>
                                <td><span style="color: var(--success-color);">✅ Required</span></td>
                            </tr>
                            <tr>
                                <td><strong>Input System Package</strong></td>
                                <td>Unity's New Input System</td>
                                <td><span style="color: var(--warning-color);">⚠️ Recommended</span></td>
                            </tr>
                            <tr>
                                <td><strong>3D Physics</strong></td>
                                <td>Enabled in Project Settings</td>
                                <td><span style="color: var(--success-color);">✅ Required</span></td>
                            </tr>
                            <tr>
                                <td><strong>Experience Level</strong></td>
                                <td>Basic Unity knowledge</td>
                                <td><span style="color: var(--info-color);">ℹ️ Helpful</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="callout callout-success">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    <div>
                        <strong>Quick Start:</strong> The system is designed to work out-of-the-box. Simply import the scripts, set up your vehicle, and you're ready to drive!
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    System Architecture
                </h3>

                <p>The JRS Vehicle Controller System is built with a <span class="highlight">modular architecture</span> that allows you to use only the components you need:</p>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Component Type</th>
                                <th>Purpose</th>
                                <th>Required</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Core Controller</strong></td>
                                <td>Main vehicle physics and movement</td>
                                <td><span style="color: var(--success-color);">✅ Yes</span></td>
                            </tr>
                            <tr>
                                <td><strong>Input System</strong></td>
                                <td>Handle player input (keyboard, mobile, gamepad)</td>
                                <td><span style="color: var(--success-color);">✅ Yes</span></td>
                            </tr>
                            <tr>
                                <td><strong>Camera Manager</strong></td>
                                <td>Multiple camera views and switching</td>
                                <td><span style="color: var(--success-color);">✅ Yes</span></td>
                            </tr>
                            <tr>
                                <td><strong>Vehicle Features</strong></td>
                                <td>Lights, doors, sirens, animations</td>
                                <td><span style="color: var(--text-muted);">❌ Optional</span></td>
                            </tr>
                            <tr>
                                <td><strong>UI Components</strong></td>
                                <td>Mobile controls and custom buttons</td>
                                <td><span style="color: var(--text-muted);">❌ Optional</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Getting Started Section -->
            <section id="getting-started" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                        </svg>
                    </div>
                    <h2>Getting Started</h2>
                </div>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                    </svg>
                    <div>
                        <strong>Quick Setup:</strong> The JRS Vehicle Controller Lite is designed for <span class="highlight">immediate use</span>. Follow these steps to get your vehicle running in minutes!
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                    </svg>
                    Installation Steps
                </h3>

                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">1</div>
                            <h3>Import Scripts</h3>
                        </div>
                        <p>Add all JRS scripts to your Unity project. Ensure all dependencies are properly imported.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669);">2</div>
                            <h3>Setup Vehicle</h3>
                        </div>
                        <p>Create or import your vehicle model with proper colliders and rigidbody component.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">3</div>
                            <h3>Add Components</h3>
                        </div>
                        <p>Attach the core JRS scripts to your vehicle GameObject and configure settings.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">4</div>
                            <h3>Configure Physics</h3>
                        </div>
                        <p>Set up wheel colliders, adjust physics parameters, and fine-tune vehicle behavior.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">5</div>
                            <h3>Setup Cameras</h3>
                        </div>
                        <p>Configure the camera system with multiple views and smooth transitions.</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">6</div>
                            <h3>Test & Refine</h3>
                        </div>
                        <p>Play test your vehicle and adjust parameters for optimal performance and feel.</p>
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    What's New in Version 2.0
                </h3>

                <div class="callout callout-warning">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m21 16-4 4-4-4"/>
                        <path d="m21 8-4-4-4 4"/>
                        <path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Important Changes:</strong> Version 2.0 removes complex vehicle selection systems in favor of <span class="highlight">direct single vehicle control</span>. This makes setup simpler and performance better.
                    </div>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Feature</th>
                                <th>Version 2.0</th>
                                <th>Previous Version</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Vehicle Control</strong></td>
                                <td><span style="color: var(--success-color);">✅ Direct control (canControl = true)</span></td>
                                <td>Required Vehicle Selector</td>
                            </tr>
                            <tr>
                                <td><strong>Vehicle Switching</strong></td>
                                <td><span style="color: var(--danger-color);">❌ Removed</span></td>
                                <td>Multiple vehicle support</td>
                            </tr>
                            <tr>
                                <td><strong>Proximity Selection</strong></td>
                                <td><span style="color: var(--danger-color);">❌ Removed</span></td>
                                <td>Auto-select nearest vehicle</td>
                            </tr>
                            <tr>
                                <td><strong>Setup Complexity</strong></td>
                                <td><span style="color: var(--success-color);">✅ Simplified</span></td>
                                <td>Complex multi-vehicle setup</td>
                            </tr>
                            <tr>
                                <td><strong>Performance</strong></td>
                                <td><span style="color: var(--success-color);">✅ Optimized</span></td>
                                <td>Higher overhead</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                    </svg>
                    Default Input Controls
                </h3>

                <div class="input-controls-grid">
                    <!-- Keyboard Controls -->
                    <div class="input-section">
                        <h4>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <rect x="2" y="6" width="20" height="12" rx="2" fill="none" stroke="currentColor" stroke-width="2"/>
                                <rect x="4" y="8" width="2" height="2" rx="0.5"/>
                                <rect x="7" y="8" width="2" height="2" rx="0.5"/>
                                <rect x="10" y="8" width="2" height="2" rx="0.5"/>
                                <rect x="13" y="8" width="2" height="2" rx="0.5"/>
                                <rect x="16" y="8" width="2" height="2" rx="0.5"/>
                                <rect x="19" y="8" width="2" height="2" rx="0.5"/>
                                <rect x="5" y="11" width="10" height="2" rx="0.5"/>
                            </svg>
                            Keyboard Controls
                        </h4>

                        <div class="input-item">
                            <div class="input-icon key-icon">W</div>
                            <div class="input-description"><strong>Forward:</strong> Accelerate vehicle</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon key-icon">S</div>
                            <div class="input-description"><strong>Backward:</strong> Reverse vehicle</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon key-icon">A</div>
                            <div class="input-description"><strong>Left:</strong> Steer left</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon key-icon">D</div>
                            <div class="input-description"><strong>Right:</strong> Steer right</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon key-icon">⎵</div>
                            <div class="input-description"><strong>Space:</strong> Handbrake</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon key-icon">H</div>
                            <div class="input-description"><strong>Headlights:</strong> Toggle headlights</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon key-icon">N</div>
                            <div class="input-description"><strong>Siren:</strong> Toggle police siren</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon key-icon">C</div>
                            <div class="input-description"><strong>Camera:</strong> Switch camera view</div>
                        </div>
                    </div>

                    <!-- Gamepad Controls -->
                    <div class="input-section">
                        <h4>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M6 9a3 3 0 0 0-3 3v4a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-4a3 3 0 0 0-3-3H6z"/>
                                <circle cx="8" cy="12" r="1"/>
                                <circle cx="16" cy="12" r="1"/>
                                <path d="M12 6v3M10 8h4"/>
                            </svg>
                            Gamepad Controls
                        </h4>

                        <div class="input-item">
                            <div class="input-icon gamepad-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <circle cx="12" cy="12" r="8"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </div>
                            <div class="input-description"><strong>Left Stick:</strong> Steering control</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon gamepad-icon">RT</div>
                            <div class="input-description"><strong>Right Trigger:</strong> Accelerate</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon gamepad-icon">LT</div>
                            <div class="input-description"><strong>Left Trigger:</strong> Reverse</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon gamepad-icon">A</div>
                            <div class="input-description"><strong>A Button:</strong> Handbrake</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon gamepad-icon">Y</div>
                            <div class="input-description"><strong>Y Button:</strong> Camera switch</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon gamepad-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3 7h7l-5.5 4 2 7L12 16l-6.5 4 2-7L2 9h7z"/>
                                </svg>
                            </div>
                            <div class="input-description"><strong>D-pad:</strong> Various toggles</div>
                        </div>
                    </div>

                    <!-- Mobile Controls -->
                    <div class="input-section">
                        <h4>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <rect x="5" y="2" width="14" height="20" rx="2" fill="none" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="18" r="1"/>
                            </svg>
                            Mobile Controls
                        </h4>

                        <div class="input-item">
                            <div class="input-icon touch-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"/>
                                    <path d="m8 12 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="input-description"><strong>Touch Buttons:</strong> All vehicle functions</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon touch-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </div>
                            <div class="input-description"><strong>Drag:</strong> Camera rotation (Orbit Camera)</div>
                        </div>

                        <div class="input-item">
                            <div class="input-icon touch-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <div class="input-description"><strong>Pinch:</strong> Zoom in/out (Orbit Camera)</div>
                        </div>
                    </div>
                </div>

                <div class="callout callout-success">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    <div>
                        <strong>Pro Tip:</strong> All input controls are fully customizable through Unity's New Input System. You can modify the VehicleInputActions.inputactions file to change key bindings.
                    </div>
                </div>
            </section>

            <!-- Core Scripts Section -->
            <section id="core-scripts" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                        </svg>
                    </div>
                    <h2>Core Scripts</h2>
                </div>

                <p>The core scripts form the foundation of the vehicle system. These are <span class="highlight">essential components</span> that every vehicle needs.</p>

                <!-- JrsVehicleController -->
                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99z"/>
                    </svg>
                    JrsVehicleController.cs
                </h3>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m21 16-4 4-4-4"/>
                        <path d="m21 8-4-4-4 4"/>
                        <path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Main Vehicle Controller:</strong> This is the heart of the system. Attach this to your main vehicle GameObject. It handles all physics, movement, and core vehicle behavior.
                    </div>
                </div>

                <h4>Key Features:</h4>
                <ul>
                    <li><strong>Realistic Physics:</strong> Advanced wheel physics with proper torque distribution</li>
                    <li><strong>Gear System:</strong> Automatic transmission with configurable gear ratios</li>
                    <li><strong>Audio Integration:</strong> Engine sounds with RPM-based pitch modulation</li>
                    <li><strong>Brake System:</strong> Separate brake and handbrake controls</li>
                    <li><strong>All-Wheel Drive:</strong> Optional AWD support</li>
                    <li><strong>Direct Control:</strong> Version 2.0 enables immediate vehicle control</li>
                </ul>

                <h4>Important Parameters:</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Description</th>
                                <th>Recommended Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>motorForce</strong></td>
                                <td>Engine power applied to wheels</td>
                                <td>1500 - 3000</td>
                            </tr>
                            <tr>
                                <td><strong>brakeForce</strong></td>
                                <td>Braking power</td>
                                <td>3000 - 5000</td>
                            </tr>
                            <tr>
                                <td><strong>maxSteerAngle</strong></td>
                                <td>Maximum steering angle (degrees)</td>
                                <td>30 - 45</td>
                            </tr>
                            <tr>
                                <td><strong>canControl</strong></td>
                                <td>Enable vehicle control (Lite: always true)</td>
                                <td>true</td>
                            </tr>
                            <tr>
                                <td><strong>useNewInputSystem</strong></td>
                                <td>Use Unity's New Input System</td>
                                <td>true (recommended)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="code-block">
<strong>Setup Example:</strong>
1. Add JrsVehicleController to your vehicle GameObject
2. Assign 4 WheelColliders to the 'wheels' array
3. Assign corresponding wheel transforms to 'wheelTransforms'
4. Set canControl = true (default in Version 2.0)
5. Configure physics parameters to match your vehicle
                </div>

                <!-- Input Controllers -->
                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                    </svg>
                    Input Controllers
                </h3>

                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15 7.5V2H9v5.5l3 3 3-3z"/>
                                </svg>
                            </div>
                            <h3>JrsNewInputController.cs</h3>
                        </div>
                        <p><strong>Recommended:</strong> Modern input controller using Unity's New Input System. Supports keyboard, gamepad, and mobile controls with advanced features.</p>
                        <ul style="margin-top: 1rem; font-size: 0.9rem;">
                            <li>✅ Unity New Input System integration</li>
                            <li>✅ Gamepad support with haptic feedback</li>
                            <li>✅ Mobile touch controls</li>
                            <li>✅ Customizable key bindings</li>
                            <li>✅ Multi-door support</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #6b7280, #4b5563);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M7.5 9H2v6h5.5l3-3-3-3z"/>
                                </svg>
                            </div>
                            <h3>JrsInputController.cs</h3>
                        </div>
                        <p><strong>Legacy:</strong> Traditional input controller using Unity's legacy Input Manager. Simple and reliable for basic setups.</p>
                        <ul style="margin-top: 1rem; font-size: 0.9rem;">
                            <li>✅ Legacy Input Manager</li>
                            <li>✅ Mobile touch controls</li>
                            <li>✅ Simple setup</li>
                            <li>❌ Limited gamepad support</li>
                            <li>❌ No advanced features</li>
                        </ul>
                    </div>
                </div>

                <div class="callout callout-warning">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m21 16-4 4-4-4"/>
                        <path d="m21 8-4-4-4 4"/>
                        <path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Choose One:</strong> Use either JrsNewInputController OR JrsInputController, not both. The New Input System version is recommended for new projects.
                    </div>
                </div>
            </section>

            <!-- Input System Section -->
            <section id="input-system" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                        </svg>
                    </div>
                    <h2>Input System</h2>
                </div>

                <p>The JRS Vehicle Controller supports both Unity's <span class="highlight">New Input System</span> and the legacy Input Manager, providing flexibility for different project requirements.</p>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                    </svg>
                    New Input System (Recommended)
                </h3>

                <div class="callout callout-success">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    <div>
                        <strong>Modern & Powerful:</strong> Unity's New Input System provides advanced features like <span class="highlight">gamepad haptics</span>, <span class="highlight">rebindable controls</span>, and <span class="highlight">multi-device support</span>.
                    </div>
                </div>

                <h4>Key Files:</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Purpose</th>
                                <th>Editable</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>VehicleInputActions.inputactions</strong></td>
                                <td>Input action definitions and key bindings</td>
                                <td><span style="color: var(--success-color);">✅ Yes</span></td>
                            </tr>
                            <tr>
                                <td><strong>VehicleInputActions.cs</strong></td>
                                <td>Auto-generated C# wrapper class</td>
                                <td><span style="color: var(--danger-color);">❌ Auto-generated</span></td>
                            </tr>
                            <tr>
                                <td><strong>JrsNewInputController.cs</strong></td>
                                <td>Main input controller script</td>
                                <td><span style="color: var(--success-color);">✅ Yes</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>Supported Input Devices:</h4>
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                </svg>
                            </div>
                            <h3>Keyboard & Mouse</h3>
                        </div>
                        <ul style="font-size: 0.9rem;">
                            <li>WASD/Arrow keys for movement</li>
                            <li>Mouse for camera control</li>
                            <li>Function keys for features</li>
                            <li>Customizable key bindings</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <h3>Gamepad</h3>
                        </div>
                        <ul style="font-size: 0.9rem;">
                            <li>Xbox, PlayStation, Generic controllers</li>
                            <li>Analog stick steering</li>
                            <li>Trigger-based acceleration</li>
                            <li>Haptic feedback support</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z"/>
                                </svg>
                            </div>
                            <h3>Mobile Touch</h3>
                        </div>
                        <ul style="font-size: 0.9rem;">
                            <li>Touch buttons for controls</li>
                            <li>Drag for camera rotation</li>
                            <li>Pinch-to-zoom support</li>
                            <li>Responsive UI scaling</li>
                        </ul>
                    </div>
                </div>

                <h4>Input Action Mappings:</h4>
                <div class="code-block">
<strong>Vehicle Movement:</strong>
• Accelerate: W, Up Arrow, Right Trigger, Touch Button
• Reverse: S, Down Arrow, Left Trigger, Touch Button
• Steer: A/D, Left/Right Arrow, Left Stick, Touch Buttons
• HandBrake: Space, A Button (Gamepad), Touch Button

<strong>Vehicle Features:</strong>
• HeadLights: H, Y Button (Gamepad), Touch Button
• Siren: N, X Button (Gamepad), Touch Button
• SignalLights: B, B Button (Gamepad), Touch Button
• ExtraLights: V, Right Shoulder, Touch Button
• SwitchCamera: C, Right Stick Click, Touch Button
• DoorToggle: O, Left Shoulder, Touch Button

<strong>Camera Controls:</strong>
• OrbitCameraRotate: Mouse Delta, Right Stick, Touch Drag
• OrbitCameraZoom: Mouse Scroll, D-pad Up/Down, Pinch

<strong>System:</strong>
• HelpToggle: F1, Start Button (Gamepad), Touch Button
                </div>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="m9 12 2 2 4-4"/>
                    </svg>
                    <div>
                        <strong>Customization:</strong> You can modify input bindings by editing the VehicleInputActions.inputactions file in Unity's Input Actions editor.
                    </div>
                </div>
            </section>

            <!-- Camera System Section -->
            <section id="camera-system" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                    </div>
                    <h2>Camera System</h2>
                </div>

                <p>The JRS Camera System provides multiple camera types with smooth transitions and advanced controls. Perfect for creating cinematic driving experiences.</p>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    JrsCameraManager.cs
                </h3>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m21 16-4 4-4-4"/>
                        <path d="m21 8-4-4-4 4"/>
                        <path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Camera Manager:</strong> Central controller that manages multiple cameras and handles smooth switching between them. Automatically configures camera targets for the active vehicle.
                    </div>
                </div>

                <h4>Key Features:</h4>
                <ul>
                    <li><strong>Auto-Configuration:</strong> Automatically finds and sets up camera targets</li>
                    <li><strong>Single Vehicle Mode:</strong> Optimized for Version 2.0 operation</li>
                    <li><strong>Smooth Transitions:</strong> Seamless camera switching</li>
                    <li><strong>Audio Management:</strong> Handles AudioListener components</li>
                    <li><strong>Target Assignment:</strong> Automatically assigns "target" and "player" objects</li>
                </ul>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    Camera Types
                </h3>

                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                                </svg>
                            </div>
                            <h3>JrsFollowCamera</h3>
                        </div>
                        <p><strong>Third-Person View:</strong> Follows the vehicle from behind with smooth spring-based movement and automatic height adjustment.</p>
                        <div class="table-container" style="margin-top: 1rem;">
                            <table style="font-size: 0.85rem;">
                                <tr><td><strong>offset</strong></td><td>Camera position relative to vehicle</td></tr>
                                <tr><td><strong>horizontalSpringConstant</strong></td><td>Spring strength (0.5 recommended)</td></tr>
                                <tr><td><strong>horizontalDampingConstant</strong></td><td>Damping factor (0.3 recommended)</td></tr>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <h3>JrsOrbitCamera</h3>
                        </div>
                        <p><strong>Free-Look Camera:</strong> Orbits around the vehicle with mouse/touch/gamepad control. Perfect for inspection and cinematic shots.</p>
                        <div class="table-container" style="margin-top: 1rem;">
                            <table style="font-size: 0.85rem;">
                                <tr><td><strong>rotationSpeed</strong></td><td>Camera rotation sensitivity</td></tr>
                                <tr><td><strong>zoomSpeed</strong></td><td>Zoom in/out speed</td></tr>
                                <tr><td><strong>minZoom/maxZoom</strong></td><td>Zoom distance limits</td></tr>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z"/>
                                </svg>
                            </div>
                            <h3>JrsDashCamera</h3>
                        </div>
                        <p><strong>First-Person View:</strong> Interior dashboard camera that follows the vehicle's movement with realistic cockpit perspective.</p>
                        <div class="table-container" style="margin-top: 1rem;">
                            <table style="font-size: 0.85rem;">
                                <tr><td><strong>cameraOffset</strong></td><td>Position inside vehicle</td></tr>
                                <tr><td><strong>cameraSensitivity</strong></td><td>Movement smoothness</td></tr>
                            </table>
                        </div>
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                    </svg>
                    Camera Controls
                </h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Control</th>
                                <th>Keyboard/Mouse</th>
                                <th>Gamepad</th>
                                <th>Mobile</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Switch Camera</strong></td>
                                <td>C key</td>
                                <td>Right Stick Click</td>
                                <td>Camera Button</td>
                            </tr>
                            <tr>
                                <td><strong>Orbit Rotation</strong></td>
                                <td>Mouse movement</td>
                                <td>Right Stick</td>
                                <td>Touch drag</td>
                            </tr>
                            <tr>
                                <td><strong>Orbit Zoom</strong></td>
                                <td>Mouse scroll wheel</td>
                                <td>D-pad Up/Down</td>
                                <td>Pinch gesture</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="callout callout-success">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    <div>
                        <strong>Setup Tip:</strong> Create child objects named "target" and "player" in your vehicle for automatic camera target assignment. The system will find and use these automatically.
                    </div>
                </div>
            </section>

            <!-- Vehicle Features Section -->
            <section id="vehicle-features" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/>
                        </svg>
                    </div>
                    <h2>Vehicle Features</h2>
                </div>

                <p>The JRS Vehicle Controller includes a comprehensive set of <span class="highlight">optional features</span> that add realism and functionality to your vehicles.</p>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                    </svg>
                    JrsVehicleLightControl.cs
                </h3>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m21 16-4 4-4-4"/>
                        <path d="m21 8-4-4-4 4"/>
                        <path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Lighting System:</strong> Complete vehicle lighting control with headlights, signal lights, brake lights, and extra lighting effects.
                    </div>
                </div>

                <h4>Light Types Supported:</h4>
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #fbbf24, #f59e0b);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                                </svg>
                            </div>
                            <h3>Headlights</h3>
                        </div>
                        <ul style="font-size: 0.9rem;">
                            <li>Main illumination lights</li>
                            <li>Toggle with H key</li>
                            <li>Supports multiple light objects</li>
                            <li>Automatic night detection</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f97316, #ea580c);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <h3>Signal Lights</h3>
                        </div>
                        <ul style="font-size: 0.9rem;">
                            <li>Turn indicators (left/right)</li>
                            <li>Hazard lights mode</li>
                            <li>Blinking animation</li>
                            <li>Toggle with B key</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99z"/>
                                </svg>
                            </div>
                            <h3>Brake Lights</h3>
                        </div>
                        <ul style="font-size: 0.9rem;">
                            <li>Automatic brake detection</li>
                            <li>Reverse lights support</li>
                            <li>Intensity based on brake force</li>
                            <li>No manual control needed</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                                </svg>
                            </div>
                            <h3>Extra Lights</h3>
                        </div>
                        <ul style="font-size: 0.9rem;">
                            <li>Additional lighting effects</li>
                            <li>Police/emergency lights</li>
                            <li>Custom light patterns</li>
                            <li>Toggle with V key</li>
                        </ul>
                    </div>
                </div>

                <h4>Setup Instructions:</h4>
                <div class="code-block">
<strong>Light Setup Process:</strong>
1. Create empty GameObjects for each light type
2. Add Light components and configure properties
3. Assign light objects to corresponding arrays in JrsVehicleLightControl
4. Configure light colors, intensity, and range
5. Test with keyboard controls (H, B, V keys)

<strong>Light Arrays:</strong>
• headLights[] - Main headlight objects
• signalLights[] - Turn signal lights
• brakeLights[] - Brake and reverse lights
• extraLights[] - Additional/emergency lights
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                    </svg>
                    JrsPoliceSiren.cs
                </h3>

                <div class="callout callout-warning">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m21 16-4 4-4-4"/>
                        <path d="m21 8-4-4-4 4"/>
                        <path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Police Siren System:</strong> Advanced siren system with multiple sound modes and visual effects. Perfect for police or emergency vehicles.
                    </div>
                </div>

                <h4>Siren Features:</h4>
                <ul>
                    <li><strong>Multiple Modes:</strong> Different siren sounds (wail, yelp, horn)</li>
                    <li><strong>Audio Control:</strong> Volume and pitch modulation</li>
                    <li><strong>Visual Effects:</strong> Synchronized light flashing</li>
                    <li><strong>Toggle Control:</strong> Easy on/off with N key</li>
                    <li><strong>Audio Source:</strong> Integrated with vehicle audio system</li>
                </ul>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Description</th>
                                <th>Recommended Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>sirenAudioSource</strong></td>
                                <td>AudioSource for siren sounds</td>
                                <td>Assign dedicated AudioSource</td>
                            </tr>
                            <tr>
                                <td><strong>sirenClips[]</strong></td>
                                <td>Array of siren sound clips</td>
                                <td>Multiple siren variations</td>
                            </tr>
                            <tr>
                                <td><strong>sirenLights[]</strong></td>
                                <td>Lights that flash with siren</td>
                                <td>Emergency light objects</td>
                            </tr>
                            <tr>
                                <td><strong>flashSpeed</strong></td>
                                <td>Light flashing frequency</td>
                                <td>2.0 - 5.0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <h3>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
                JrsDoorMechanic.cs
            </h3>

            <div class="callout callout-success">
                <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 12l2 2 4-4"/>
                    <circle cx="12" cy="12" r="10"/>
                </svg>
                <div>
                    <strong>Door System:</strong> Realistic door mechanics with smooth animations and individual door control. Supports multiple doors with customizable open/close behavior.
                </div>
            </div>

            <h4>Door Features:</h4>
            <div class="card-grid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                            </svg>
                        </div>
                        <h3>Individual Control</h3>
                    </div>
                    <ul style="font-size: 0.9rem;">
                        <li>6 doors supported (Front L/R, Rear L/R, Hood, Trunk)</li>
                        <li>Keys 1-6 for individual doors</li>
                        <li>O key for all doors toggle</li>
                        <li>Smooth animation system</li>
                    </ul>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <h3>Realistic Animation</h3>
                    </div>
                    <ul style="font-size: 0.9rem;">
                        <li>Configurable open angles</li>
                        <li>Smooth lerp-based movement</li>
                        <li>Customizable animation speed</li>
                        <li>Audio feedback support</li>
                    </ul>
                </div>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Door Type</th>
                            <th>Key Binding</th>
                            <th>Typical Open Angle</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Front Left Door</strong></td>
                            <td>1 key</td>
                            <td>70° - 90°</td>
                        </tr>
                        <tr>
                            <td><strong>Front Right Door</strong></td>
                            <td>2 key</td>
                            <td>70° - 90°</td>
                        </tr>
                        <tr>
                            <td><strong>Rear Left Door</strong></td>
                            <td>3 key</td>
                            <td>70° - 90°</td>
                        </tr>
                        <tr>
                            <td><strong>Rear Right Door</strong></td>
                            <td>4 key</td>
                            <td>70° - 90°</td>
                        </tr>
                        <tr>
                            <td><strong>Hood</strong></td>
                            <td>5 key</td>
                            <td>45° - 60°</td>
                        </tr>
                        <tr>
                            <td><strong>Trunk</strong></td>
                            <td>6 key</td>
                            <td>60° - 90°</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Setup Guide Section -->
            <section id="setup-guide" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                        </svg>
                    </div>
                    <h2>Complete Setup Guide</h2>
                </div>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                    </svg>
                    <div>
                        <strong>Step-by-Step Setup:</strong> Follow this comprehensive guide to set up your vehicle from scratch. Each step is crucial for proper functionality.
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                    </svg>
                    Step 1: Vehicle GameObject Setup
                </h3>

                <div class="code-block">
<strong>Vehicle Hierarchy:</strong>
VehicleRoot (Main GameObject)
├── VehicleBody (3D Model)
├── WheelColliders
│   ├── FrontLeft_WheelCollider
│   ├── FrontRight_WheelCollider
│   ├── RearLeft_WheelCollider
│   └── RearRight_WheelCollider
├── WheelMeshes
│   ├── FrontLeft_Wheel
│   ├── FrontRight_Wheel
│   ├── RearLeft_Wheel
│   └── RearRight_Wheel
├── Cameras
│   ├── FollowCamera
│   ├── OrbitCamera
│   └── DashCamera
├── target (Empty GameObject)
├── player (Empty GameObject)
└── Lights (Optional)
    ├── Headlights
    ├── SignalLights
    ├── BrakeLights
    └── ExtraLights
                </div>

                <h4>Required Components on Main GameObject:</h4>
                <ul>
                    <li><strong>Rigidbody:</strong> Mass: 1000-2000, Drag: 0.3, Angular Drag: 3</li>
                    <li><strong>Collider:</strong> Box or Mesh collider for vehicle body</li>
                    <li><strong>JrsVehicleController:</strong> Main vehicle script</li>
                    <li><strong>JrsNewInputController:</strong> Input handling (recommended)</li>
                    <li><strong>JrsCameraManager:</strong> Camera management</li>
                </ul>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                    Step 2: Wheel Collider Configuration
                </h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Property</th>
                                <th>Front Wheels</th>
                                <th>Rear Wheels</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Mass</strong></td>
                                <td>20</td>
                                <td>20</td>
                                <td>Unsprung mass</td>
                            </tr>
                            <tr>
                                <td><strong>Radius</strong></td>
                                <td>0.3 - 0.4</td>
                                <td>0.3 - 0.4</td>
                                <td>Match wheel mesh</td>
                            </tr>
                            <tr>
                                <td><strong>Wheel Damping Rate</strong></td>
                                <td>0.25</td>
                                <td>0.25</td>
                                <td>Damping force</td>
                            </tr>
                            <tr>
                                <td><strong>Suspension Distance</strong></td>
                                <td>0.3</td>
                                <td>0.3</td>
                                <td>Travel distance</td>
                            </tr>
                            <tr>
                                <td><strong>Force App Point Distance</strong></td>
                                <td>0.0</td>
                                <td>0.0</td>
                                <td>Center of wheel</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>Suspension Spring Settings:</h4>
                <div class="code-block">
<strong>Spring Configuration:</strong>
• Spring Force: 35000 (adjust based on vehicle weight)
• Damper: 4500 (controls bounce)
• Target Position: 0.5 (neutral position)

<strong>Forward/Sideways Friction:</strong>
• Extremum Slip: 0.4 / 0.2
• Extremum Value: 1.0 / 1.0
• Asymptote Slip: 0.8 / 0.5
• Asymptote Value: 0.5 / 0.75
• Stiffness: 1.0 / 1.0
                </div>
            </section>

            <!-- Troubleshooting Section -->
            <section id="troubleshooting" class="section">
                <div class="section-header">
                    <div class="section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                        </svg>
                    </div>
                    <h2>Troubleshooting</h2>
                </div>

                <p>Common issues and their solutions when working with the JRS Vehicle Controller Lite system.</p>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99z"/>
                    </svg>
                    Vehicle Control Issues
                </h3>

                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <h3>Vehicle Won't Move</h3>
                        </div>
                        <div class="callout callout-danger" style="margin: 1rem 0;">
                            <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m21 16-4 4-4-4"/>
                                <path d="m21 8-4-4-4 4"/>
                                <path d="M4 12h14"/>
                            </svg>
                            <div>
                                <strong>Solutions:</strong>
                                <ul style="margin-top: 0.5rem;">
                                    <li>Check <span class="highlight">canControl = true</span> in JrsVehicleController</li>
                                    <li>Verify wheel colliders are properly assigned</li>
                                    <li>Ensure motorForce > 0 (recommended: 1500-3000)</li>
                                    <li>Check if Rigidbody is not kinematic</li>
                                    <li>Verify input system is receiving input</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2z"/>
                                </svg>
                            </div>
                            <h3>Poor Handling</h3>
                        </div>
                        <div class="callout callout-warning" style="margin: 1rem 0;">
                            <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m21 16-4 4-4-4"/>
                                <path d="m21 8-4-4-4 4"/>
                                <path d="M4 12h14"/>
                            </svg>
                            <div>
                                <strong>Adjustments:</strong>
                                <ul style="margin-top: 0.5rem;">
                                    <li>Reduce <span class="highlight">maxSteerAngle</span> (try 30-35°)</li>
                                    <li>Adjust wheel collider friction curves</li>
                                    <li>Lower center of mass in Rigidbody</li>
                                    <li>Increase suspension spring force</li>
                                    <li>Fine-tune damper values</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                            </div>
                            <h3>Camera Issues</h3>
                        </div>
                        <div class="callout callout-info" style="margin: 1rem 0;">
                            <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="m9 12 2 2 4-4"/>
                            </svg>
                            <div>
                                <strong>Fixes:</strong>
                                <ul style="margin-top: 0.5rem;">
                                    <li>Ensure "target" and "player" objects exist</li>
                                    <li>Check JrsCameraManager is attached</li>
                                    <li>Verify camera scripts are on camera objects</li>
                                    <li>Only one AudioListener should be active</li>
                                    <li>Check camera switching input bindings</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
                    </svg>
                    Input System Problems
                </h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Problem</th>
                                <th>Cause</th>
                                <th>Solution</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>No Input Response</strong></td>
                                <td>Input System not enabled</td>
                                <td>Enable New Input System in Project Settings</td>
                            </tr>
                            <tr>
                                <td><strong>Gamepad Not Working</strong></td>
                                <td>Controller not recognized</td>
                                <td>Check Input System device support</td>
                            </tr>
                            <tr>
                                <td><strong>Mobile Controls Missing</strong></td>
                                <td>UI Canvas not configured</td>
                                <td>Set up mobile UI with proper Canvas settings</td>
                            </tr>
                            <tr>
                                <td><strong>Key Bindings Wrong</strong></td>
                                <td>Input Actions misconfigured</td>
                                <td>Edit VehicleInputActions.inputactions file</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"/>
                    </svg>
                    Performance Optimization
                </h3>

                <div class="callout callout-success">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    <div>
                        <strong>Optimization Tips:</strong> Version 2.0 is already optimized, but here are additional performance improvements you can make.
                    </div>
                </div>

                <ul>
                    <li><strong>Reduce Physics Timestep:</strong> Use Fixed Timestep of 0.02 (50Hz) for better performance</li>
                    <li><strong>Optimize Wheel Colliders:</strong> Use simpler friction curves when possible</li>
                    <li><strong>LOD System:</strong> Implement Level of Detail for distant vehicles</li>
                    <li><strong>Audio Optimization:</strong> Use compressed audio formats for engine sounds</li>
                    <li><strong>Light Culling:</strong> Disable lights when vehicle is far from camera</li>
                    <li><strong>Rigidbody Settings:</strong> Set Interpolate to "Interpolate" for smooth movement</li>
                </ul>

                <h3>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                    Getting Help
                </h3>

                <div class="callout callout-info">
                    <svg class="callout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="m9 12 2 2 4-4"/>
                    </svg>
                    <div>
                        <strong>Support Resources:</strong> When you need additional help beyond this documentation.
                    </div>
                </div>

                <ul>
                    <li><strong>Unity Console:</strong> Check for error messages and warnings</li>
                    <li><strong>Debug Mode:</strong> Enable debug logs in vehicle controller scripts</li>
                    <li><strong>Unity Forums:</strong> Search for similar issues in Unity community</li>
                    <li><strong>Documentation:</strong> Review Unity's official WheelCollider documentation</li>
                    <li><strong>Test Scene:</strong> Create a minimal test scene to isolate issues</li>
                </ul>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p><strong>JRS Vehicle Physics Controller - Version 2.0</strong></p>
            <p>Complete Documentation Guide - Updated & Modernized</p>
            <p style="margin-top: 1rem; font-size: 0.9rem;">
                <strong>Font Attribution:</strong> This document uses the Inter and JetBrains Mono fonts from Google Fonts,
                both licensed under the SIL Open Font License (OFL).
                <br>
                Inter by Rasmus Andersson | JetBrains Mono by JetBrains
            </p>
            <p style="margin-top: 0.5rem; font-size: 0.85rem; opacity: 0.7;">
                All SVG icons are custom-created for this documentation. No external icon libraries were used.
            </p>
        </div>
    </footer>
</body>
</html>
