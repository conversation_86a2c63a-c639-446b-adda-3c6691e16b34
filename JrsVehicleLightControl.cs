using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class JrsVehicleLightControl : MonoBehaviour
{
    [Header("Light Elements")]
    public GameObject[] headlightElements;
    public GameObject[] signalElements;
    public GameObject[] extraLightsElements;
    public GameObject[] reverseLightsElements;
    public GameObject[] brakeLightsElements;

    private bool lightsOn = false;
    private bool signalOn = false;
    private bool extraLightsOn = false;

    private float flickerSpeed = 0.5f;
    private bool isFlickering = false;

    private JrsInputController mobileInputController;
    private JrsNewInputController newInputController;
    private JrsVehicleController vehicleController;

    void Start()
    {
        mobileInputController = FindObjectOfType<JrsInputController>();
        newInputController = FindObjectOfType<JrsNewInputController>();
        vehicleController = GetComponent<JrsVehicleController>();
    }

    void Update()
    {
        // Lite version - always process inputs (single vehicle)
        if (vehicleController == null || !vehicleController.canControl)
            return;

        // Process inputs using New Input System
        if (GetHeadLightsInput())
        {
            lightsOn = !lightsOn;
            ToggleLights();
        }

        if (GetSignalLightsInput())
        {
            signalOn = !signalOn;
            ToggleSignal();
        }

        if (GetExtraLightsInput())
        {
            extraLightsOn = !extraLightsOn;
            ToggleExtraLights();
        }
    }

    // Public methods for controlling lights
    public void SetBrakeLights(bool state)
    {
        foreach (GameObject element in brakeLightsElements)
        {
            if (element != null)
            {
                element.SetActive(state);
            }
        }
    }

    public void SetReverseLights(bool state)
    {
        foreach (GameObject element in reverseLightsElements)
        {
            if (element != null)
            {
                element.SetActive(state);
            }
        }
    }

    public void SetHeadlights(bool state)
    {
        lightsOn = state;
        ToggleLights();
    }

    void ToggleLights()
    {
        // Lite version - direct control without vehicle selector
        foreach (GameObject element in headlightElements)
        {
            if (element != null)
            {
                element.SetActive(lightsOn);
            }
        }
    }

    void ToggleSignal()
    {
        // Lite version - direct control without vehicle selector
        if (signalOn)
        {
            StartFlickering();
        }
        else
        {
            StopFlickering();
        }

        foreach (GameObject element in signalElements)
        {
            if (element != null)
            {
                element.SetActive(signalOn);
            }
        }
    }

    void ToggleExtraLights()
    {
        // Lite version - direct control without vehicle selector


        foreach (GameObject element in extraLightsElements)
        {
            if (element != null)
            {
                element.SetActive(extraLightsOn);
            }
        }
    }

    void ToggleReverseLights(bool isOn)
    {
        foreach (GameObject element in reverseLightsElements)
        {
            if (element != null)
            {
                element.SetActive(isOn);
            }
        }
    }

    void StartFlickering()
    {
        if (!isFlickering)
        {
            isFlickering = true;
            StartCoroutine(FlickerCoroutine());
        }
    }

    void StopFlickering()
    {
        if (isFlickering)
        {
            isFlickering = false;
            StopCoroutine(FlickerCoroutine());
        }
    }

    IEnumerator FlickerCoroutine()
    {
        while (isFlickering)
        {
            foreach (GameObject element in signalElements)
            {
                if (element != null)
                {
                    element.SetActive(!element.activeSelf);
                }
            }
            yield return new WaitForSeconds(flickerSpeed);
        }
    }

    // Input helper methods that work with both old and new input systems
    private bool GetHeadLightsInput()
    {
        // Try New Input System first
        if (newInputController != null)
        {
            return newInputController.GetHeadLightsInput();
        }

        // Fallback to old input system and mobile controls
        return Input.GetKeyDown(KeyCode.H) ||
               (mobileInputController != null && mobileInputController.headLightsButton.IsButtonClicked());
    }

    private bool GetSignalLightsInput()
    {
        // Try New Input System first
        if (newInputController != null)
        {
            return newInputController.GetSignalLightsInput();
        }

        // Fallback to old input system and mobile controls
        return Input.GetKeyDown(KeyCode.T) ||
               (mobileInputController != null && mobileInputController.signalLightsButton.IsButtonClicked());
    }

    private bool GetExtraLightsInput()
    {
        // Try New Input System first
        if (newInputController != null)
        {
            return newInputController.GetExtraLightsInput();
        }

        // Fallback to old input system and mobile controls
        return Input.GetKeyDown(KeyCode.E) ||
               (mobileInputController != null && mobileInputController.extraLightsButton.IsButtonClicked());
    }
}
