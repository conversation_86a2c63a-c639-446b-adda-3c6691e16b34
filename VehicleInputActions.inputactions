{"name": "VehicleInputActions", "maps": [{"name": "Vehicle", "id": "a8b5c2d1-e3f4-5678-9abc-def012345678", "actions": [{"name": "Accelerate", "type": "<PERSON><PERSON>", "id": "b1c2d3e4-f5a6-7890-bcde-f123456789ab", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "id": "c2d3e4f5-a6b7-8901-cdef-23456789abcd", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Reverse", "type": "<PERSON><PERSON>", "id": "d3e4f5a6-b7c8-9012-defa-3456789abcde", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Steer", "type": "Value", "id": "e4f5a6b7-c8d9-0123-efab-456789abcdef", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "HandBrake", "type": "<PERSON><PERSON>", "id": "f5a6b7c8-d9e0-1234-fabc-56789abcdef0", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "HeadLights", "type": "<PERSON><PERSON>", "id": "a6b7c8d9-e0f1-2345-abcd-6789abcdef01", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "id": "b7c8d9e0-f1a2-3456-bcde-789abcdef012", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "SignalLights", "type": "<PERSON><PERSON>", "id": "c8d9e0f1-a2b3-4567-cdef-89abcdef0123", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "ExtraLights", "type": "<PERSON><PERSON>", "id": "d9e0f1a2-b3c4-5678-defa-9abcdef01234", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "SwitchCamera", "type": "<PERSON><PERSON>", "id": "e0f1a2b3-c4d5-6789-efab-abcdef012345", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "DoorToggle", "type": "<PERSON><PERSON>", "id": "f1a2b3c4-d5e6-789a-fabc-def012345678", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Door1Toggle", "type": "<PERSON><PERSON>", "id": "a1b2c3d4-e5f6-789a-bcde-f012345678ab", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Door2Toggle", "type": "<PERSON><PERSON>", "id": "b2c3d4e5-f6a7-890b-cdef-0123456789bc", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Door3Toggle", "type": "<PERSON><PERSON>", "id": "c3d4e5f6-a7b8-901c-defa-123456789bcd", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Door4Toggle", "type": "<PERSON><PERSON>", "id": "d4e5f6a7-b8c9-012d-efab-23456789bcde", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Door5Toggle", "type": "<PERSON><PERSON>", "id": "e5f6a7b8-c9d0-123e-fabc-3456789bcdef", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Door6Toggle", "type": "<PERSON><PERSON>", "id": "f6a7b8c9-d0e1-234f-abcd-456789bcdef0", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "OrbitCameraRotate", "type": "Value", "id": "a1b2c3d4-e5f6-7890-abcd-ef0123456789", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "OrbitCameraZoom", "type": "Value", "id": "b2c3d4e5-f6a7-8901-bcde-f01234567890", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "HelpToggle", "type": "<PERSON><PERSON>", "id": "d4e5f6a7-b8c9-0123-defa-123456789012", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "f1a2b3c4-d5e6-789a-fabc-bcdef0123456", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Accelerate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a2b3c4d5-e6f7-89ab-abcd-cdef01234567", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Accelerate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "733b5cac-d490-47b1-97f9-5e28ed33e9cd", "path": "<Gamepad>/rightTrigger", "interactions": "", "processors": "", "groups": "", "action": "Accelerate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b3c4d5e6-f7a8-9abc-bcde-def012345678", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard", "action": "HandBrake", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "fddb621c-9e23-4963-a619-712efe254a5e", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "", "action": "HandBrake", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c4d5e6f7-a8b9-abcd-cdef-ef0123456789", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Reverse", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d5e6f7a8-b9ca-bcde-defa-f01234567890", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Reverse", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "41734a50-f4e7-46be-81ae-c7487dcdcc03", "path": "<Gamepad>/leftTrigger", "interactions": "", "processors": "", "groups": "", "action": "Reverse", "isComposite": false, "isPartOfComposite": false}, {"name": "1D Axis", "id": "e6f7a8b9-cadb-cdef-efab-012345678901", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "Steer", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "f7a8b9ca-dbec-defa-fabc-123456789012", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Steer", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "a8b9cadb-ecfd-efab-abcd-234567890123", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Steer", "isComposite": false, "isPartOfComposite": true}, {"name": "1D Axis", "id": "b9cadbec-fdae-fabc-bcde-345678901234", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "Steer", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "cadbecfd-aefb-abcd-cdef-456789012345", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Steer", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "dbecfdae-fbac-bcde-defa-567890123456", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Steer", "isComposite": false, "isPartOfComposite": true}, {"name": "1D Axis", "id": "4b1b7679-734e-4dc5-ba7e-abe014a54720", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "Steer", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "8f2908ec-9bc4-4bde-b9b2-ab17a689d874", "path": "<Gamepad>/leftStick/left", "interactions": "", "processors": "", "groups": "", "action": "Steer", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "aed7dc8f-2d9d-460b-a4e4-1dd874c84024", "path": "<Gamepad>/leftStick/right", "interactions": "", "processors": "", "groups": "", "action": "Steer", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "ecfdaefb-acbd-cdef-efab-678901234567", "path": "<Keyboard>/h", "interactions": "", "processors": "", "groups": "Keyboard", "action": "HeadLights", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7e7ce99b-58c6-4856-a5bc-20ad0a28d68d", "path": "<Gamepad>/rightShoulder", "interactions": "", "processors": "", "groups": "", "action": "HeadLights", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "fdaefbac-bdce-defa-fabc-789012345678", "path": "<Keyboard>/p", "interactions": "", "processors": "", "groups": "Keyboard", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7fa8ea03-748f-4330-9c3f-8d8444ba36b3", "path": "<Gamepad>/buttonNorth", "interactions": "", "processors": "", "groups": "", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "aefbacbd-cedf-efab-abcd-890123456789", "path": "<Keyboard>/t", "interactions": "", "processors": "", "groups": "Keyboard", "action": "SignalLights", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5753a800-83b8-4443-8bf4-2a83cdf3194d", "path": "<Gamepad>/leftShoulder", "interactions": "", "processors": "", "groups": "", "action": "SignalLights", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "fbacbdce-dfea-fabc-bcde-901234567890", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "Keyboard", "action": "ExtraLights", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6679889e-2556-4af4-801e-944783590ef8", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": "", "action": "ExtraLights", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "acbdcedf-eafb-abcd-cdef-012345678901", "path": "<Keyboard>/#(C)", "interactions": "", "processors": "", "groups": "Keyboard", "action": "SwitchCamera", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7adbab63-6e89-4179-85e6-f22962e4e66c", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "", "action": "SwitchCamera", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1a79b056-adeb-4f0c-a9c7-c37784489373", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard", "action": "<PERSON><PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6bebdbc1-2599-4f86-8128-297a98031db5", "path": "<Gamepad>/leftTrigger", "interactions": "", "processors": "", "groups": "", "action": "<PERSON><PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2b8ac167-befc-5f1d-b8d8-d48895590484", "path": "<Keyboard>/o", "interactions": "", "processors": "", "groups": "Keyboard", "action": "DoorToggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3c9bd278-cfed-6f2e-c9e9-e59906601595", "path": "<Keyboard>/numpad4", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Door1Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4b703fe3-df52-408b-b8b9-07a1b870b039", "path": "<Gamepad>/dpad/left", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Door1Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4d0ce389-d0fe-7f3f-d0f0-f60017712606", "path": "<Keyboard>/numpad5", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Door2Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1c08192d-cced-41ad-9693-ba9eb0328f23", "path": "<Gamepad>/dpad/right", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Door2Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5e1df490-e1af-8f4a-e1a1-a71128823717", "path": "<Keyboard>/numpad1", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Door3Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6f2ea501-f2ba-9f5b-f2b2-b82239934828", "path": "<Keyboard>/numpad2", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Door4Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7a3fb612-a3cb-0a6c-a3c3-c93340045939", "path": "<Keyboard>/5", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Door5Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8b4ac723-b4dc-1b7d-b4d4-d04451156040", "path": "<Keyboard>/6", "interactions": "", "processors": "", "groups": "Keyboard", "action": "Door6Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c3d4e5f6-a7b8-9012-cdef-123456789abc", "path": "<Mouse>/delta", "interactions": "", "processors": "", "groups": "Keyboard", "action": "OrbitCameraRotate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d4e5f6a7-b8c9-0123-defa-23456789abcd", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "OrbitCameraRotate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e5f6a7b8-c9d0-1234-efab-3456789abcde", "path": "<Mouse>/scroll/y", "interactions": "", "processors": "", "groups": "Keyboard", "action": "OrbitCameraZoom", "isComposite": false, "isPartOfComposite": false}, {"name": "Zoom In/Out", "id": "f6a7b8c9-d0e1-2345-fabc-456789abcdef", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "OrbitCameraZoom", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "0af7d856-bae1-4419-a4c5-73c911bb4048", "path": "<Gamepad>/dpad/down", "interactions": "", "processors": "", "groups": "Gamepad", "action": "OrbitCameraZoom", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "b8c9d0e1-f2a3-4567-bcde-6789abcdef01", "path": "<Gamepad>/dpad/up", "interactions": "", "processors": "", "groups": "Gamepad", "action": "OrbitCameraZoom", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "e6f7a8b9-c0d1-2345-efab-6789abcdef01", "path": "<Keyboard>/f1", "interactions": "", "processors": "", "groups": "Keyboard", "action": "HelpToggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f7a8b9c0-d1e2-3456-fabc-789abcdef012", "path": "<Gamepad>/start", "interactions": "", "processors": "", "groups": "Gamepad", "action": "HelpToggle", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard", "bindingGroup": "Keyboard", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Touch", "bindingGroup": "Touch", "devices": [{"devicePath": "<Touchscreen>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}]}