<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upcomming Updates:New JRS Vehicle Controller System for Unity - Complete Guide</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #f3f3f3 0%, #d1d1d1 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 4px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea, #4b67a2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            color: #718096;
            max-width: 800px;
            margin: 0 auto;
        }

        .content-section {
            max-width: 1200px;
            background: rgba(255, 255, 255, 0.95);
            margin: 0 auto 40px auto;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea, #4b67a2);
            color: white;
            padding: 20px 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-header h2 {
            font-size: 1.8rem;
            font-weight: 600;
        }

        .section-content {
            padding: 30px;
        }

        .toc {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .toc-item {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-decoration: none;
            color: #2d3748;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .toc-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .toc-item h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .toc-item p {
            font-size: 0.9rem;
            color: #718096;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .feature-card h4 {
            font-size: 1rem;
            font-weight: 600;
            margin-top: 10px;
            color: #2d3748;
        }

        .feature-card p {
            font-size: 0.85rem;
            color: #718096;
            margin-top: 5px;
        }

        .callout {
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .callout-tip {
            background: #f0fff4;
            border-left-color: #38a169;
            color: #22543d;
        }

        .callout-warning {
            background: #fffbf0;
            border-left-color: #ed8936;
            color: #744210;
        }

        .callout-info {
            background: #ebf8ff;
            border-left-color: #3182ce;
            color: #2a4365;
        }

        .callout-important {
            background: #fef5e7;
            border-left-color: #d69e2e;
            color: #744210;
        }

        .parameter-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .parameter-table th,
        .parameter-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .parameter-table th {
            background: #f7fafc;
            font-weight: 600;
            color: #2d3748;
        }

        .parameter-table tr:hover {
            background: #f7fafc;
        }

        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 15px 0;
        }

        .highlight {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }

        .icon {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
        }

        .footer {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 40px;
            backdrop-filter: blur(10px);
        }

        .footer p {
            color: #718096;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }
            
            .toc {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Introducing the Next Generation: New JRS Vehicle Controller for Unity (Coming Soon)</h1>
            <p>Complete beginner's guide to setting up and using the JRS Vehicle Controller System in Unity. Learn how to create realistic vehicle physics, controls, and features step by step.</p>
        </div>

        <!-- Table of Contents -->
        <div class="content-section">
            <div class="section-header">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/>
                </svg>
                <h2>Table of Contents</h2>
            </div>
            <div class="section-content">
                <div class="toc">
                    <a href="#getting-started" class="toc-item">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="5,3 19,12 5,21 5,3"/>
                        </svg>
                        <div>
                            <h3>Getting Started</h3>
                            <p>Basic setup and requirements</p>
                        </div>
                    </a>
                    <a href="#core-scripts" class="toc-item">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/>
                        </svg>
                        <div>
                            <h3>Core Scripts</h3>
                            <p>Vehicle controller and input systems</p>
                        </div>
                    </a>
                    <a href="#camera-system" class="toc-item">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/><circle cx="12" cy="13" r="4"/>
                        </svg>
                        <div>
                            <h3>Camera System</h3>
                            <p>Multiple camera types and management</p>
                        </div>
                    </a>
                    <a href="#vehicle-features" class="toc-item">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="16" y2="12"/><line x1="12" x2="12.01" y1="8" y2="8"/>
                        </svg>
                        <div>
                            <h3>Vehicle Features</h3>
                            <p>Lights, sirens, doors, and animations</p>
                        </div>
                    </a>
                    <a href="#ui-interaction" class="toc-item">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                        </svg>
                        <div>
                            <h3>UI & Interaction</h3>
                            <p>Custom buttons and user interface</p>
                        </div>
                    </a>
                    <a href="#utility-scripts" class="toc-item">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
                        </svg>
                        <div>
                            <h3>Utility Scripts</h3>
                            <p>Helper scripts and additional tools</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Feature Showcase -->
        <div class="content-section">
            <div class="section-header">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26 12,2"/>
                </svg>
                <h2>System Features</h2>
            </div>
            <div class="section-content">
                <div class="features-grid">
                    <div class="feature-card">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #667eea;">
                            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/>
                        </svg>
                        <h4>Realistic Physics</h4>
                        <p>Advanced wheel colliders and suspension</p>
                    </div>
                    <div class="feature-card">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #38a169;">
                            <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                        </svg>
                        <h4>Dual Input Systems</h4>
                        <p>Legacy and New Input System support</p>
                    </div>
                    <div class="feature-card">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #ed8936;">
                            <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/><circle cx="12" cy="13" r="4"/>
                        </svg>
                        <h4>Multiple Cameras</h4>
                        <p>Follow, orbit, and dashboard views</p>
                    </div>
                    <div class="feature-card">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #3182ce;">
                            <circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                        </svg>
                        <h4>Vehicle Lights</h4>
                        <p>Headlights, brake lights, and signals</p>
                    </div>
                    <div class="feature-card">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #d69e2e;">
                            <path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"/><path d="M21 19c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"/>
                        </svg>
                        <h4>Police Features</h4>
                        <p>Sirens and emergency lighting</p>
                    </div>
                    <div class="feature-card">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #805ad5;">
                            <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><path d="M9 3v18"/><path d="m16 15-3-3 3-3"/>
                        </svg>
                        <h4>Interactive Doors</h4>
                        <p>Animated door mechanics with audio</p>
                    </div>
                    <div class="feature-card">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #e53e3e;">
                            <circle cx="12" cy="12" r="10"/><polyline points="12,6 12,12 16,14"/>
                        </svg>
                        <h4>Steering Animation</h4>
                        <p>Realistic steering wheel rotation</p>
                    </div>
                    <div class="feature-card">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #38b2ac;">
                            <path d="M3 3v18h18"/><path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"/>
                        </svg>
                        <h4>Multi-Vehicle</h4>
                        <p>Switch between multiple vehicles</p>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- Getting Started Section -->
        <div class="content-section" id="getting-started">
            <div class="section-header">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polygon points="5,3 19,12 5,21 5,3"/>
                </svg>
                <h2>Getting Started</h2>
            </div>
            <div class="section-content">
                <div class="callout callout-info">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="16" y2="12"/><line x1="12" x2="12.01" y1="8" y2="8"/>
                    </svg>
                    <div>
                        <strong>Welcome to JRS Vehicle Controller!</strong> This system provides realistic vehicle physics and controls for Unity projects. Perfect for racing games, simulators, and any project requiring vehicle mechanics.
                    </div>
                </div>

                <h3>Prerequisites</h3>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li><strong>Unity Version:</strong> 2021.3 LTS or newer</li>
                    <li><strong>Input System:</strong> Unity's New Input System package (optional but recommended)</li>
                    <li><strong>Physics:</strong> 3D Physics enabled in project settings</li>
                    <li><strong>Experience Level:</strong> Basic Unity knowledge helpful but not required</li>
                </ul>

                <h3>Quick Setup Steps</h3>
                <div class="callout callout-tip">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div>
                        <strong>Pro Tip:</strong> Follow these steps in order for the smoothest setup experience. Each step builds on the previous one.
                    </div>
                </div>

                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li><strong>Import Scripts:</strong> Add all JRS scripts to your project</li>
                    <li><strong>Setup Vehicle:</strong> Create or import your vehicle model</li>
                    <li><strong>Add Components:</strong> Attach the core JRS scripts</li>
                    <li><strong>Configure Physics:</strong> Set up wheel colliders and rigidbody</li>
                    <li><strong>Setup Cameras:</strong> Configure camera system</li>
                    <li><strong>Test & Refine:</strong> Play test and adjust settings</li>
                </ol>

                <h3>System Architecture</h3>
                <p>The JRS Vehicle Controller System is built with a modular architecture:</p>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Component Type</th>
                            <th>Purpose</th>
                            <th>Required</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Core Controller</strong></td>
                            <td>Main vehicle physics and movement</td>
                            <td>✅ Yes</td>
                        </tr>
                        <tr>
                            <td><strong>Input System</strong></td>
                            <td>Handle player input (keyboard, mobile, gamepad)</td>
                            <td>✅ Yes</td>
                        </tr>
                        <tr>
                            <td><strong>Camera Manager</strong></td>
                            <td>Multiple camera views and switching</td>
                            <td>✅ Yes</td>
                        </tr>
                        <tr>
                            <td><strong>Vehicle Features</strong></td>
                            <td>Lights, doors, sirens, animations</td>
                            <td>❌ Optional</td>
                        </tr>
                        <tr>
                            <td><strong>UI Components</strong></td>
                            <td>Mobile controls and custom buttons</td>
                            <td>❌ Optional</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Core Scripts Section -->
        <div class="content-section" id="core-scripts">
            <div class="section-header">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/>
                </svg>
                <h2>Core Scripts</h2>
            </div>
            <div class="section-content">
                <p>The core scripts form the foundation of the vehicle system. These are <span class="highlight">essential components</span> that every vehicle needs.</p>

                <!-- JrsVehicleController -->
                <h3>🚗 JrsVehicleController.cs</h3>
                <div class="callout callout-important">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m21 16-4 4-4-4"/><path d="m21 8-4-4-4 4"/><path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Main Vehicle Controller:</strong> This is the heart of the system. Attach this to your main vehicle GameObject. It handles all physics, movement, and core vehicle behavior.
                    </div>
                </div>

                <h4>Purpose & Usage</h4>
                <p>The <strong>JrsVehicleController</strong> manages:</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Vehicle physics and movement</li>
                    <li>Wheel collider management</li>
                    <li>Gear shifting and transmission</li>
                    <li>Engine sound and RPM calculation</li>
                    <li>Brake and reverse light control</li>
                </ul>

                <h4>Key Parameters</h4>
                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Recommended Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>motorForce</strong></td>
                            <td>float</td>
                            <td>Engine power - higher values = more acceleration</td>
                            <td>1500-3000</td>
                        </tr>
                        <tr>
                            <td><strong>maxSteerAngleFront</strong></td>
                            <td>float</td>
                            <td>Maximum steering angle for front wheels</td>
                            <td>30-45 degrees</td>
                        </tr>
                        <tr>
                            <td><strong>maxSteerAngleRear</strong></td>
                            <td>float</td>
                            <td>Rear wheel steering (for realism)</td>
                            <td>5-15 degrees</td>
                        </tr>
                        <tr>
                            <td><strong>brakeForce</strong></td>
                            <td>float</td>
                            <td>Braking power</td>
                            <td>3000-5000</td>
                        </tr>
                        <tr>
                            <td><strong>enableAllWheelDrive</strong></td>
                            <td>bool</td>
                            <td>Enable AWD vs FWD/RWD</td>
                            <td>false (RWD)</td>
                        </tr>
                        <tr>
                            <td><strong>wheels</strong></td>
                            <td>WheelCollider[]</td>
                            <td>Array of 4 wheel colliders</td>
                            <td>Front Left, Front Right, Rear Left, Rear Right</td>
                        </tr>
                        <tr>
                            <td><strong>wheelTransforms</strong></td>
                            <td>Transform[]</td>
                            <td>Visual wheel meshes</td>
                            <td>Match wheel collider order</td>
                        </tr>
                    </tbody>
                </table>

                <div class="callout callout-tip">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div>
                        <strong>Setup Tip:</strong> Start with default values and adjust based on your vehicle's weight and desired feel. Heavier vehicles need higher motorForce values.
                    </div>
                </div>

                <h4>How to Attach & Configure</h4>
                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li>Select your main vehicle GameObject</li>
                    <li>Add Component → JrsVehicleController</li>
                    <li>Ensure your vehicle has a Rigidbody component</li>
                    <li>Create 4 empty GameObjects as children for wheel colliders</li>
                    <li>Add WheelCollider components to each wheel GameObject</li>
                    <li>Assign wheel colliders to the <strong>wheels</strong> array</li>
                    <li>Assign visual wheel meshes to <strong>wheelTransforms</strong> array</li>
                    <li>Set <strong>centerOfMassObject</strong> to an empty GameObject positioned at vehicle's center</li>
                </ol>

                <!-- Input Controllers -->
                <h3>🎮 Input Controllers</h3>
                <p>The JRS system supports both legacy and modern input systems. Choose the one that fits your project needs.</p>

                <h4>JrsInputController.cs (Legacy Input)</h4>
                <div class="callout callout-info">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="16" y2="12"/><line x1="12" x2="12.01" y1="8" y2="8"/>
                    </svg>
                    <div>
                        <strong>Legacy Input System:</strong> Uses Unity's traditional Input Manager. Simple to set up and works with keyboard and mobile touch controls.
                    </div>
                </div>

                <p><strong>Key Features:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Keyboard input (WASD, Arrow keys)</li>
                    <li>Mobile touch button support</li>
                    <li>Camera switching</li>
                    <li>Light and siren controls</li>
                </ul>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>accelerateButton</strong></td>
                            <td>JrsCustomButton</td>
                            <td>Mobile accelerate button reference</td>
                        </tr>
                        <tr>
                            <td><strong>brakeButton</strong></td>
                            <td>JrsCustomButton</td>
                            <td>Mobile brake button reference</td>
                        </tr>
                        <tr>
                            <td><strong>leftButton/rightButton</strong></td>
                            <td>JrsCustomButton</td>
                            <td>Mobile steering buttons</td>
                        </tr>
                        <tr>
                            <td><strong>steerSpeed</strong></td>
                            <td>float</td>
                            <td>How fast steering responds (recommended: 2-5)</td>
                        </tr>
                        <tr>
                            <td><strong>cameraManager</strong></td>
                            <td>JrsCameraManager</td>
                            <td>Reference to camera management system</td>
                        </tr>
                    </tbody>
                </table>

                <h4>JrsNewInputController.cs (New Input System)</h4>
                <div class="callout callout-tip">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div>
                        <strong>Recommended Choice:</strong> Uses Unity's New Input System for better gamepad support, rebindable controls, and more flexibility. Supports hybrid mobile + keyboard/gamepad input.
                    </div>
                </div>

                <p><strong>Advanced Features:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Gamepad support (Xbox, PlayStation, etc.)</li>
                    <li>Rebindable key mappings</li>
                    <li>Multi-door support (up to 6 doors)</li>
                    <li>Hybrid mobile + desktop controls</li>
                    <li>Event-driven input handling</li>
                </ul>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>vehicleInputActions</strong></td>
                            <td>VehicleInputActions</td>
                            <td>Input action asset (auto-generated)</td>
                        </tr>
                        <tr>
                            <td><strong>enableMobileControls</strong></td>
                            <td>bool</td>
                            <td>Enable hybrid mobile + desktop support</td>
                        </tr>
                        <tr>
                            <td><strong>door1Button - door6Button</strong></td>
                            <td>JrsCustomButton</td>
                            <td>Individual door control buttons</td>
                        </tr>
                    </tbody>
                </table>

                <div class="callout callout-warning">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m21 16-4 4-4-4"/><path d="m21 8-4-4-4 4"/><path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Important:</strong> Only use ONE input controller per vehicle. Using both will cause conflicts. The New Input System requires the Input System package to be installed.
                    </div>
                </div>

                <h4>VehicleInputActions.cs & .inputactions</h4>
                <p>These files define the input mappings for the New Input System:</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>VehicleInputActions.cs:</strong> Auto-generated C# wrapper</li>
                    <li><strong>VehicleInputActions.inputactions:</strong> Input action definitions</li>
                </ul>

                <div class="code-block">
Default Key Mappings:
• WASD / Arrow Keys: Vehicle movement
• Space: Handbrake
• H: Headlights toggle
• N: Siren toggle
• B: Signal lights toggle
• V: Extra lights toggle
• C: Camera switch
• O: Door toggle
• 1-6: Individual door controls
                </div>
            </div>
        </div>

        <!-- Camera System Section -->
        <div class="content-section" id="camera-system">
            <div class="section-header">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/><circle cx="12" cy="13" r="4"/>
                </svg>
                <h2>Camera System</h2>
            </div>
            <div class="section-content">
                <p>The camera system provides multiple viewing angles and smooth transitions. It's designed to work seamlessly with the vehicle controller.</p>

                <div class="callout callout-info">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="16" y2="12"/><line x1="12" x2="12.01" y1="8" y2="8"/>
                    </svg>
                    <div>
                        <strong>Camera Architecture:</strong> The system uses a manager pattern with multiple camera types. Only one camera is active at a time, with smooth switching between views.
                    </div>
                </div>

                <h3>📹 JrsCameraManager.cs</h3>
                <p><strong>Purpose:</strong> Central hub for managing multiple cameras and handling camera switching.</p>

                <h4>Key Features</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Automatic camera switching with input</li>
                    <li>AudioListener management (prevents conflicts)</li>
                    <li>Dynamic camera target assignment</li>
                    <li>Support for unlimited camera types</li>
                </ul>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>cameras</strong></td>
                            <td>Camera[]</td>
                            <td>Array of all available cameras</td>
                        </tr>
                    </tbody>
                </table>

                <h4>Setup Instructions</h4>
                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li>Create an empty GameObject named "CameraManager"</li>
                    <li>Add the JrsCameraManager component</li>
                    <li>Create camera GameObjects as children</li>
                    <li>Add appropriate camera scripts to each camera</li>
                    <li>Assign all cameras to the <strong>cameras</strong> array</li>
                    <li>Ensure only the first camera has AudioListener enabled</li>
                </ol>

                <h3>🎥 Camera Types</h3>

                <h4>JrsFollowCamera.cs - Third Person Follow</h4>
                <div class="callout callout-tip">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div>
                        <strong>Most Popular:</strong> This is the classic third-person camera that follows behind the vehicle. Great for racing games and general driving.
                    </div>
                </div>

                <p><strong>Features:</strong> Smooth following with spring physics, automatic height adjustment, always looks at vehicle.</p>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Recommended</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>target</strong></td>
                            <td>Transform</td>
                            <td>Vehicle target point (usually "target" child object)</td>
                            <td>Required</td>
                        </tr>
                        <tr>
                            <td><strong>offset</strong></td>
                            <td>Vector3</td>
                            <td>Camera position relative to target</td>
                            <td>(0, 2, -6)</td>
                        </tr>
                        <tr>
                            <td><strong>horizontalSpringConstant</strong></td>
                            <td>float</td>
                            <td>How quickly camera follows horizontally</td>
                            <td>0.5</td>
                        </tr>
                        <tr>
                            <td><strong>horizontalDampingConstant</strong></td>
                            <td>float</td>
                            <td>Reduces camera oscillation</td>
                            <td>0.3</td>
                        </tr>
                    </tbody>
                </table>

                <h4>JrsOrbitCamera.cs - Free Look Orbit</h4>
                <p><strong>Features:</strong> Mouse/touch controlled orbital camera with zoom. Perfect for showcasing vehicles or detailed inspection.</p>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Recommended</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>target</strong></td>
                            <td>Transform</td>
                            <td>Vehicle target point to orbit around</td>
                            <td>Required</td>
                        </tr>
                        <tr>
                            <td><strong>rotationSpeed</strong></td>
                            <td>float</td>
                            <td>Mouse sensitivity for rotation</td>
                            <td>5.0</td>
                        </tr>
                        <tr>
                            <td><strong>distance</strong></td>
                            <td>float</td>
                            <td>Distance from target</td>
                            <td>10.0</td>
                        </tr>
                        <tr>
                            <td><strong>minZoom/maxZoom</strong></td>
                            <td>float</td>
                            <td>Zoom limits</td>
                            <td>5.0 / 15.0</td>
                        </tr>
                    </tbody>
                </table>

                <h4>JrsDashCamera.cs - First Person Interior</h4>
                <p><strong>Features:</strong> Interior dashboard view that moves with the vehicle. Great for immersive driving experience.</p>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Recommended</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>player</strong></td>
                            <td>Transform</td>
                            <td>Vehicle's "player" position (driver seat)</td>
                            <td>Required</td>
                        </tr>
                        <tr>
                            <td><strong>cameraOffset</strong></td>
                            <td>Vector3</td>
                            <td>Position relative to player transform</td>
                            <td>(0, 1.5, -0.5)</td>
                        </tr>
                        <tr>
                            <td><strong>cameraSensitivity</strong></td>
                            <td>float</td>
                            <td>How smoothly camera follows vehicle rotation</td>
                            <td>2.0</td>
                        </tr>
                    </tbody>
                </table>

                <div class="callout callout-tip">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div>
                        <strong>Camera Setup Tip:</strong> Create empty GameObjects named "target" and "player" as children of your vehicle. Position "target" at the vehicle's center and "player" at the driver's seat location.
                    </div>
                </div>
            </div>
        </div>

        <!-- Vehicle Features Section -->
        <div class="content-section" id="vehicle-features">
            <div class="section-header">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="16" y2="12"/><line x1="12" x2="12.01" y1="8" y2="8"/>
                </svg>
                <h2>Vehicle Features</h2>
            </div>
            <div class="section-content">
                <p>These optional components add realistic vehicle features like lighting, doors, sirens, and animations.</p>

                <h3>💡 JrsVehicleLightControl.cs</h3>
                <div class="callout callout-info">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="16" y2="12"/><line x1="12" x2="12.01" y1="8" y2="8"/>
                    </svg>
                    <div>
                        <strong>Lighting System:</strong> Manages all vehicle lights including headlights, brake lights, turn signals, and extra lighting. Automatically responds to vehicle state and player input.
                    </div>
                </div>

                <h4>Features</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Headlights:</strong> Toggle on/off with input</li>
                    <li><strong>Brake Lights:</strong> Automatic activation when braking</li>
                    <li><strong>Reverse Lights:</strong> Automatic when in reverse</li>
                    <li><strong>Signal Lights:</strong> Turn signals/hazard lights</li>
                    <li><strong>Extra Lights:</strong> Additional lighting (fog lights, etc.)</li>
                </ul>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>headLights</strong></td>
                            <td>GameObject[]</td>
                            <td>Headlight GameObjects (lights/meshes)</td>
                        </tr>
                        <tr>
                            <td><strong>brakeLights</strong></td>
                            <td>GameObject[]</td>
                            <td>Brake light GameObjects</td>
                        </tr>
                        <tr>
                            <td><strong>reverseLights</strong></td>
                            <td>GameObject[]</td>
                            <td>Reverse light GameObjects</td>
                        </tr>
                        <tr>
                            <td><strong>signalLights</strong></td>
                            <td>GameObject[]</td>
                            <td>Turn signal GameObjects</td>
                        </tr>
                        <tr>
                            <td><strong>extraLights</strong></td>
                            <td>GameObject[]</td>
                            <td>Additional lights (fog, auxiliary, etc.)</td>
                        </tr>
                    </tbody>
                </table>

                <h4>Setup Instructions</h4>
                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li>Add JrsVehicleLightControl to your vehicle GameObject</li>
                    <li>Create light GameObjects (can be Light components or just meshes)</li>
                    <li>Assign light GameObjects to appropriate arrays</li>
                    <li>Lights will automatically toggle based on vehicle state and input</li>
                </ol>

                <h3>🚨 JrsPoliceSiren.cs</h3>
                <div class="callout callout-tip">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div>
                        <strong>Emergency Vehicle Feature:</strong> Perfect for police cars, ambulances, and fire trucks. Provides flashing emergency lights and siren sounds.
                    </div>
                </div>

                <h4>Features</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Alternating red and blue emergency lights</li>
                    <li>Siren audio with looping</li>
                    <li>Automatic light flashing patterns</li>
                    <li>Integration with vehicle selector system</li>
                </ul>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>sirenSound</strong></td>
                            <td>AudioSource</td>
                            <td>Audio source for siren sound</td>
                        </tr>
                        <tr>
                            <td><strong>redLights</strong></td>
                            <td>GameObject[]</td>
                            <td>Red emergency light GameObjects</td>
                        </tr>
                        <tr>
                            <td><strong>blueLights</strong></td>
                            <td>GameObject[]</td>
                            <td>Blue emergency light GameObjects</td>
                        </tr>
                    </tbody>
                </table>

                <h3>🚪 JrsDoorMechanic.cs</h3>
                <p><strong>Purpose:</strong> Provides realistic door opening/closing mechanics with smooth animation and audio feedback.</p>

                <h4>Features</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Smooth rotational door animation</li>
                    <li>Configurable rotation axis (X, Y, or Z)</li>
                    <li>Individual door control with unique IDs</li>
                    <li>Open/close audio effects</li>
                    <li>Multi-door support (up to 6 doors per vehicle)</li>
                </ul>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Recommended</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>angle</strong></td>
                            <td>float</td>
                            <td>Door opening angle in degrees</td>
                            <td>90</td>
                        </tr>
                        <tr>
                            <td><strong>speed</strong></td>
                            <td>float</td>
                            <td>Door opening/closing speed</td>
                            <td>90</td>
                        </tr>
                        <tr>
                            <td><strong>axisToRotate</strong></td>
                            <td>Axis</td>
                            <td>Rotation axis (X, Y, or Z)</td>
                            <td>Y</td>
                        </tr>
                        <tr>
                            <td><strong>doorID</strong></td>
                            <td>string</td>
                            <td>Unique identifier ("Door1", "Door2", etc.)</td>
                            <td>Door1</td>
                        </tr>
                        <tr>
                            <td><strong>openSound/closeSound</strong></td>
                            <td>AudioSource</td>
                            <td>Audio for door opening/closing</td>
                            <td>Optional</td>
                        </tr>
                    </tbody>
                </table>

                <div class="callout callout-warning">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m21 16-4 4-4-4"/><path d="m21 8-4-4-4 4"/><path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Door Setup:</strong> Attach this script to each door GameObject (not the main vehicle). Each door needs a unique doorID. The door will rotate around its pivot point.
                    </div>
                </div>

                <h3>🎛️ JrsSteeringWheelAnimator.cs</h3>
                <p><strong>Purpose:</strong> Animates the steering wheel to match player input, adding visual realism to interior views.</p>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Recommended</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>steeringWheelTransform</strong></td>
                            <td>Transform</td>
                            <td>The steering wheel GameObject to animate</td>
                            <td>Required</td>
                        </tr>
                        <tr>
                            <td><strong>maxSteerAngle</strong></td>
                            <td>float</td>
                            <td>Maximum steering wheel rotation</td>
                            <td>450 (1.25 turns)</td>
                        </tr>
                        <tr>
                            <td><strong>rotationSpeed</strong></td>
                            <td>float</td>
                            <td>How fast the wheel rotates</td>
                            <td>100</td>
                        </tr>
                    </tbody>
                </table>

                <h3>🔒 JrsLockRotation.cs</h3>
                <p><strong>Purpose:</strong> Utility script that keeps objects (like brake calipers) aligned with wheel rotation while maintaining their position.</p>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>wheel_mesh</strong></td>
                            <td>Transform</td>
                            <td>Reference wheel mesh to follow</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- UI & Interaction Section -->
        <div class="content-section" id="ui-interaction">
            <div class="section-header">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                </svg>
                <h2>UI & Interaction</h2>
            </div>
            <div class="section-content">
                <h3>📱 JrsCustomButton.cs</h3>
                <div class="callout callout-info">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="16" y2="12"/><line x1="12" x2="12.01" y1="8" y2="8"/>
                    </svg>
                    <div>
                        <strong>Mobile Touch Controls:</strong> Essential for mobile games. Provides touch-responsive buttons that work with the input controllers.
                    </div>
                </div>

                <h4>Features</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Touch press/release detection</li>
                    <li>Click event handling</li>
                    <li>Works with Unity UI system</li>
                    <li>Compatible with both input controllers</li>
                </ul>

                <h4>Public Methods</h4>
                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Returns</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>IsButtonPressed()</strong></td>
                            <td>bool</td>
                            <td>True while button is being held down</td>
                        </tr>
                        <tr>
                            <td><strong>IsButtonReleased()</strong></td>
                            <td>bool</td>
                            <td>True when button is not pressed</td>
                        </tr>
                        <tr>
                            <td><strong>IsButtonClicked()</strong></td>
                            <td>bool</td>
                            <td>True for one frame when button is clicked</td>
                        </tr>
                    </tbody>
                </table>

                <h4>Setup Instructions</h4>
                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li>Create UI Canvas if not already present</li>
                    <li>Add Button UI elements for mobile controls</li>
                    <li>Add JrsCustomButton component to each button</li>
                    <li>Assign buttons to input controller references</li>
                    <li>Position buttons for comfortable mobile use</li>
                </ol>

                <div class="callout callout-tip">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div>
                        <strong>Mobile UI Tip:</strong> Place steering buttons on the left side and acceleration/brake on the right. Keep buttons large enough for thumbs and test on actual mobile devices.
                    </div>
                </div>
            </div>
        </div>

        <!-- Utility Scripts Section -->
        <div class="content-section" id="utility-scripts">
            <div class="section-header">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
                </svg>
                <h2>Utility Scripts</h2>
            </div>
            <div class="section-content">
                <h3>🚗 Vehicle System - Lite Version</h3>
                <div class="callout callout-important">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m21 16-4 4-4-4"/><path d="m21 8-4-4-4 4"/><path d="M4 12h14"/>
                    </svg>
                    <div>
                        <strong>Lite Version:</strong> Streamlined single-vehicle system. Vehicle Selector and Proximity Selector functionality has been removed for simplified gameplay.
                    </div>
                </div>

                <h4>Features</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Switch vehicles with Page Up/Page Down keys</li>
                    <li>Click-to-select vehicle functionality</li>
                    <li>Automatic camera target assignment</li>
                    <li>Prevents multiple vehicles from being controlled simultaneously</li>
                </ul>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>vehicles</strong></td>
                            <td>List&lt;JrsVehicleController&gt;</td>
                            <td>List of all available vehicles</td>
                        </tr>
                        <tr>
                            <td><strong>cameraManager</strong></td>
                            <td>JrsCameraManager</td>
                            <td>Camera system to update when switching</td>
                        </tr>
                    </tbody>
                </table>

                <h3>🔄 JrsObjectRotator.cs</h3>
                <p><strong>Purpose:</strong> Simple utility for continuously rotating objects. Great for spinning objects, fans, or decorative elements.</p>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Example</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>rotationAxis</strong></td>
                            <td>Vector3</td>
                            <td>Axis of rotation</td>
                            <td>Vector3.up (Y-axis)</td>
                        </tr>
                        <tr>
                            <td><strong>rotationSpeed</strong></td>
                            <td>float</td>
                            <td>Degrees per second</td>
                            <td>90 (quarter turn/sec)</td>
                        </tr>
                    </tbody>
                </table>

                <h3>👁️ JrsToggleObject.cs</h3>
                <p><strong>Purpose:</strong> Toggle GameObject visibility with a key press. Useful for showing/hiding UI elements or objects.</p>

                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>objectToToggle</strong></td>
                            <td>GameObject</td>
                            <td>Object to show/hide</td>
                        </tr>
                        <tr>
                            <td><strong>toggleKey</strong></td>
                            <td>KeyCode</td>
                            <td>Key to press for toggle</td>
                        </tr>
                    </tbody>
                </table>

                <h3>🚪 JrsExitApplication.cs</h3>
                <p><strong>Purpose:</strong> Handles application exit and window management. Essential for standalone builds.</p>

                <h4>Features</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>ESC Key:</strong> Exit application (or stop play mode in editor)</li>
                    <li><strong>F11 Key:</strong> Toggle fullscreen mode</li>
                    <li><strong>F10 Key:</strong> Resize window (Windows only)</li>
                </ul>

                <div class="callout callout-tip">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div>
                        <strong>Setup Tip:</strong> Add this to an empty GameObject in your scene. It will automatically handle exit functionality across different platforms.
                    </div>
                </div>

                <h3>📋 Complete Setup Checklist</h3>
                <div class="callout callout-info">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="16" y2="12"/><line x1="12" x2="12.01" y1="8" y2="8"/>
                    </svg>
                    <div>
                        <strong>Final Checklist:</strong> Use this checklist to ensure your vehicle is properly configured and ready for testing.
                    </div>
                </div>

                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h4>✅ Vehicle Setup Checklist</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>☐ Vehicle GameObject with Rigidbody</li>
                        <li>☐ 4 WheelColliders positioned correctly</li>
                        <li>☐ JrsVehicleController attached and configured</li>
                        <li>☐ Input controller (JrsInputController OR JrsNewInputController)</li>
                        <li>☐ Camera system with JrsCameraManager</li>
                        <li>☐ At least one camera (JrsFollowCamera recommended)</li>
                        <li>☐ "target" and "player" child objects positioned</li>
                        <li>☐ Vehicle configured for direct control (lite version)</li>
                        <li>☐ Mobile UI buttons (if needed) with JrsCustomButton</li>
                        <li>☐ JrsExitApplication in scene</li>
                        <li>☐ Test drive and adjust parameters</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Font Attribution:</strong> This documentation uses Inter and JetBrains Mono fonts from Google Fonts, both licensed under the Open Font License (OFL).</p>
            <p>Created for Unity JRS Vehicle Controller System - Complete Beginner's Guide</p>
        </div>
    </div>
</body>
</html>
